import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/pages/login/login.dart';
import 'package:student_end_flutter/pages/order/order_page.dart';
import 'package:student_end_flutter/utils/event_bus_utils.dart';
import 'package:student_end_flutter/utils/sharedPreferences_util.dart';
import 'package:student_end_flutter/utils/httpRequest.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:tx_im/dx_utils/IMHttpRequest.dart';
import 'package:tx_im/im_home_page.dart';
import 'package:tx_im/im_module_service.dart';
import '../../../utils/native_channel_utils.dart';
import '../../main/home_page.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class BaseController extends GetxController
    with GetSingleTickerProviderStateMixin, WidgetsBindingObserver {
  // 选中的页面索引
  var currentIndex = 0.obs;
  // 未读消息数
  final unreadMessageCount = 0.obs;
  // 页面列表
  final List<Widget> pages = [
    HomePage(),
    OrderPage(),
    IMModuleHomePage(),
  ].obs;

  final List pagesRes = [
    {
      "normal": "assets/tab/icon_shouye.png",
      "chose": "assets/tab/icon_shouye_s.png",
      "lbl": "首页"
    },
    {
      "normal": "assets/tab/icon_jiedan.png",
      "chose": "assets/tab/icon_jiedan_s.png",
      "lbl": "接单"
    },
    {
      "normal": "assets/tab/icon_xiaoxi.png",
      "chose": "assets/tab/icon_xiaoxi_s.png",
      "lbl": "消息"
    },
  ].obs;

  TabController? tabController;
  RxBool isLogin = false.obs;

  @override
  void onInit() {
    WidgetsBinding.instance.addObserver(this);
    initWeChatUnInstall();
    isLogin.value = UserOption.token.isNotEmpty ? true : false;
    tabController = TabController(
      initialIndex: currentIndex.value,
      length: pages.length,
      vsync: this,
    );
    tabController?.addListener(_onTabChanged);

    // 初始获取未读消息数
    IMModuleService().getTotalUnreadCount().then((value) {
      unreadMessageCount.value = value;
    });

    // 监听未读消息数变化
    IMModuleService().unreadMsgCountNotifier.addListener(() {
      unreadMessageCount.value = IMModuleService().unreadMsgCountNotifier.value;
    });

    super.onInit();
  }

  initWeChatUnInstall() {
    NativeBridge.registerHandler(NativeMethods.initWeChatUnInstall,
        (params) async {
      ToastUtil.showShortErrorToast("微信未安装");
    });
    NativeBridge.registerHandler(
        NativeMethods.takingOrderSuccess, (params) async {});

    NativeBridge.registerHandler(NativeMethods.exitLogin, (params) async {
      UserOption.cleanLocalData();
      TIMUIKitCore.getInstance().logout();
      SharedPreferencesUtil.deleteData(SharedPreferencesUtil.isAppRoleValue);
      UserOption.cleanData();
      Get.off(() => LoginPage());
    });
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // 重新设置token到HTTP headers
      if (UserOption.token.isNotEmpty && UserOption.tokenKey.isNotEmpty) {
        HttpUtil.heads[UserOption.tokenKey] = UserOption.token;
        IMHttpUtil.heads = HttpUtil.heads;
      }

      // 判断im是否登录状态
      V2TIMManager().getLoginStatus().then((V2TimValueCallback value) async {
        if (value.code != 0) {
          IMModuleService().TIMLogin();
        } else if (Platform.isIOS) {
          IMModuleService().TIMLogin();
        }
      });
      EventBusUtils.sendMsg("reloadOrderList");
      isLogin.value = UserOption.token.isNotEmpty ? true : false;
    } else if (state == AppLifecycleState.paused) {
    } else if (state == AppLifecycleState.inactive) {
    } else if (state == AppLifecycleState.detached) {
      // 不杀死 im数据会有问题
      exit(0);
    }
  }

  _onTabChanged() {
    if (tabController?.index == 0) {
      currentIndex.value = tabController!.index;
      return;
    }
    if (tabController?.index != currentIndex.value) {
      if (UserOption.checkNoTokenJump()) {
        return;
      }
      currentIndex.value = tabController!.index;
    }
  }

  // 添加这个方法
  void ensureOnHomeTab() {
    if (currentIndex.value != 0) {
      changeTab(0);
    }
  }

  changeTab(index) {
    if (index == 0) {
      tabController?.animateTo(index);
      currentIndex.value = index;
      return;
    }
    if (index == 1) {
      tabController?.animateTo(index);
      currentIndex.value = index;
      return;
    }
    if (UserOption.checkNoTokenJump()) {
      return;
    }
    tabController?.animateTo(index);
    currentIndex.value = index;
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    tabController?.removeListener(() {});
    tabController?.dispose();
    // 移除未读消息监听器
    IMModuleService().unreadMsgCountNotifier.removeListener(() {});
    super.onClose();
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }
}
