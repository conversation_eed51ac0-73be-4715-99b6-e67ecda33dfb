name: student_end_flutter
description: "鼎教教"
publish_to: "none" # Remove this line if you wish to publish to pub.dev
# TODO 后续迭代需要版本+1  0731上线 2.1.3+17
version: 2.1.6+20

environment:
  sdk: ^3.5.0

dependencies:
  flutter:
    sdk: flutter
  easy_debounce: ^2.0.3
  get: ^4.6.1
  dio: ^5.0.3
  bot_toast: ^4.0.3
  flutter_screenutil: ^5.6.0
  cupertino_icons: ^1.0.8
  flutter_carousel_widget: ^3.1.0
  flutter_staggered_grid_view: ^0.7.0
  cached_network_image: ^3.2.0
  contained_tab_bar_view: ^0.8.0
  image: ^3.0.1
  shared_preferences: ^2.3.0
  connectivity_plus: ^6.1.3
  event_bus: ^2.0.1
  url_launcher: ^6.3.1
  modal_bottom_sheet: ^3.0.0
  crypto: ^3.0.1
  webview_flutter: ^4.2.2
  #  image_gallery_saver: ^2.0.3
  permission_handler: ^10.4.5
  # 打开系统设置页面
  app_settings: ^6.1.1
  empty_widget: ^0.0.5
  device_info_plus: ^10.1.2
  tx_im:
    path: ./tx-im
  flutter_linkify: ^6.0.0
  package_info_plus: ^8.0.0
  archive: ^3.4.10
  path_provider: ^2.1.1
dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  flutter_native_splash: ^2.2.16
flutter_native_splash:
  color: "#ffffff"
  image: "assets/ic_launcher.png"
  android: true
  ios: true
  ios_content_mode: center
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  fonts:
    - family: R
      fonts:
        - asset: assets/fonts/regular.ttf
    - family: M
      fonts:
        - asset: assets/fonts/medium.ttf
  assets:
    - tx-im/assets/images/
    - assets/
    - assets/login/
    - assets/tab/
    - android/app/src/main/
    - android/app/src/main/res/
    - android/app/src/main/res/layout/
    - android/app/src/main/res/values/
    - android/app/src/main/res/drawable/
    - android/app/src/main/res/values-zh/
    - android/app/src/main/res/mipmap-hdpi/
    - android/app/src/main/res/mipmap-mdpi/
    - android/app/src/main/res/values-land/
    - android/app/src/main/res/drawable-v21/
    - android/app/src/main/res/mipmap-xhdpi/
    - android/app/src/main/res/values-night/
    - android/app/src/main/res/mipmap-xxhdpi/
    - android/app/src/main/res/mipmap-xxxhdpi/
    - android/app/src/main/java/
    - android/app/src/main/java/io/
    - android/app/src/main/java/io/flutter/
    - android/app/src/main/java/io/flutter/plugins/
    - android/app/src/main/java/com/
    - android/app/src/main/java/com/dxznjy/
    - android/app/src/main/assets/
    - android/app/src/main/assets/apps/

    - android/app/src/main/assets/data/
    - assets/calling_message/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
