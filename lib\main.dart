import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:student_end_flutter/utils/android_ios_plugin.dart';
import 'package:student_end_flutter/utils/httpRequest.dart';
import 'package:student_end_flutter/pages/splash/splash_page.dart';
import 'package:tx_im/provider/local_setting.dart';
import 'package:tx_im/provider/login_user_Info.dart';
import 'package:tx_im/provider/theme.dart';
import 'package:tx_im/provider/user_guide_provider.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:provider/provider.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'common/Config.dart';
import '../../utils/config_service.dart';

void main() async {
  await ScreenUtil.ensureScreenSize();
  WidgetsFlutterBinding.ensureInitialized();
  LocaleSettings.useDeviceLocale();

  runApp(
    // runAutoApp(
    TranslationProvider(
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => LoginUserInfo()),
          ChangeNotifierProvider(create: (_) => DefaultThemeData()),
          ChangeNotifierProvider(
            create: (_) => LocalSetting(),
          ),
          ChangeNotifierProvider(create: (_) => UserGuideProvider()),
        ],
        child:MyApp(),
      ),
    ),
  );

}



class MyApp extends StatefulWidget {
  const MyApp({super.key});

  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey();
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {

  var wgt_app_id = '';

  var wgt_url = '';

  var wgt_version = '';

  @override
  void initState() {
    ConfigService.getDjjShareConfig();
    super.initState();

    checkWgtVersion();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      locale: TranslationProvider.of(context).flutterLocale,
      supportedLocales: LocaleSettings.supportedLocales,
      localizationsDelegates: GlobalMaterialLocalizations.delegates,
      title: '鼎教教',
      builder: BotToastInit(),
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.white),
        useMaterial3: true,
      ),
      navigatorKey: MyApp.navigatorKey,
      home: SplashPage(),
      navigatorObservers: [TUICallKit.navigatorObserver],
    );
  }


  checkWgtVersion () async {
    var response = await HttpUtil().get("${Config.checkWgtVersion}?enCode=DJJ");
    if (response != null) {
      if (response is Map) {
        if (response.containsKey('APP_ID')) {
          wgt_app_id = response['APP_ID'];
        }
        if (response.containsKey('WGT_URL')) {
          wgt_url = response['WGT_URL'];
        }
        if (response.containsKey('APP_VERSION')) {
          wgt_version = response['APP_VERSION'];
        }
      }
      String wgtVersion = await AndroidIosPlugin.getWgtVersion();
      if (wgt_version != wgtVersion) {
        BotToast.showLoading();
        await AndroidIosPlugin.updateWgt(
            appId: wgt_app_id, wgtUrl: wgt_url, version: wgt_version);
      }
    }
  }
}
