import 'package:bot_toast/bot_toast.dart';
import 'package:get/get.dart';
import 'dart:convert';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/utils/android_ios_plugin.dart';
import 'package:student_end_flutter/utils/app_version_util.dart';
import 'package:student_end_flutter/utils/sharedPreferences_util.dart';
import 'package:tx_im/user.dart';
import '../../../common/Config.dart';
import '../../../utils/httpRequest.dart';
import '../../login/login.dart';

class HomeController extends GetxController
    with GetSingleTickerProviderStateMixin {

  String wgt_app_id = '';

  String wgt_url = '';

  String wgt_version = '';

  Map<String, dynamic> loginInfo = {
    "isAppRoleValue": '1',
    "tel": "1",
    "token": 'token',
    "studentCode": "625021113",
    "isQy": true,
    "isLogin": 'isLogin',
    "baseUrl":'',
    // 版本
    'appVersion': AppVersionUtil.appVersion ?? '',
  };

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
  }

  getTeacherInfo() async {
    var responseInfo = await HttpUtil().get(Config.getAppUserInfo);
    SharedPreferencesUtil.saveData(SharedPreferencesUtil.isAppRoleValue,
        responseInfo['appRoleValue'].toString());

  }
  cancelLogin(){
    UserOption.cleanData();
    UserOption.cleanLocalData();
    Get.off(() => LoginPage());
  }

  openTeacherUni(context) async {
    //todo 后面带入登录后获取的数据
    BotToast.showLoading();
    if (UserOption.checkNoTokenJump()) {
      BotToast.closeAllLoading();
      return;
    }

    String isAppRoleValue = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.isAppRoleValue);
    if( isAppRoleValue=='visitor'){
      BotToast.closeAllLoading();
      UserOption.showDeliverDialog(context,'请联系管理员开通助教权限',() => cancelLogin());
      return;
    }

    // await checkWgtVersion();

    String imLoginToken = TimUser.loginToken;
    if( imLoginToken.isEmpty){
      imLoginToken = await SharedPreferencesUtil.getData<String>(
          SharedPreferencesUtil.token);
    }
    UserOption.token = imLoginToken;
    loginInfo['token'] = UserOption.token;
    SharedPreferencesUtil.saveData(SharedPreferencesUtil.token, imLoginToken);

    await getTeacherInfo();

    String key = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.isAppRoleValue);
    // HttpUtil.heads = IMHttpUtil.heads;
    if (key.isNotEmpty) {
      loginInfo['isAppRoleValue'] = key;
    }
    String t =
        await SharedPreferencesUtil.getData<String>(SharedPreferencesUtil.tel);
    if (t != null && t != "") {
      loginInfo['tel'] = t;
    }
    loginInfo['baseUrl']=Config.URL;
    AndroidIosPlugin.openUniApp(json.encode(loginInfo));
    // AndroidIosPlugin.openUniApp('/qyWechat/takingOrder?path=qyWechat/formalClass&id=1370051588255522816&deliverMerchant=8250326152&token=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');
  }


  checkWgtVersion () async {
    var response = await HttpUtil().get("${Config.checkWgtVersion}?enCode=DJJ");
    if (response != null) {
      if (response is Map) {
        if (response.containsKey('APP_ID')) {
          wgt_app_id = response['APP_ID'];
        }
        if (response.containsKey('WGT_URL')) {
          wgt_url = response['WGT_URL'];
        }
        if (response.containsKey('APP_VERSION')) {
          wgt_version = response['APP_VERSION'];
        }
      }
      String wgtVersion = await AndroidIosPlugin.getWgtVersion();
      if (wgt_version != wgtVersion) {
        BotToast.showLoading();
        await AndroidIosPlugin.updateWgt(
            appId: wgt_app_id, wgtUrl: wgt_url, version: wgt_version);
      }
    }
  }
}


